import 'server-only';
import {
  BlobServiceClient,
  ContainerClient,
  StorageSharedKeyCredential,
  BlobSASPermissions,
  SASProtocol,
  generateBlobSASQueryParameters,
  BlobBatchClient,
} from "@azure/storage-blob";
import { QueueClient, StorageSharedKeyCredential as QueueStorageSharedKeyCredential } from "@azure/storage-queue";
import {
  DataLakeServiceClient,
  DataLakeSASSignatureValues,
  DataLakeSASPermissions,
  generateDataLakeSASQueryParameters,
} from "@azure/storage-file-datalake";

function required(name: string) {
  const v = process.env[name];
  if (!v) throw new Error(`[env] Missing ${name}`);
  return v;
}

const account = required("AZURE_STORAGE_ACCOUNT");
const accountKey = required("AZURE_STORAGE_ACCOUNT_KEY");
const containerName = required("AZURE_BLOB_CONTAINER");
const queueName = required("AZURE_QUEUE_NAME");

const credential = new StorageSharedKeyCredential(account, accountKey);
const blobService = new BlobServiceClient(`https://${account}.blob.core.windows.net`, credential);

export function getContainerClient(): ContainerClient {
  return blobService.getContainerClient(containerName);
}

export type UploadSasInput = {
  blob?: string;
  blobName?: string;
  container?: string;
  size?: number;
  ttlSeconds?: number; // 動態 TTL（大檔略長、小檔較短）
  ttlMinutes?: number; // Alternative TTL in minutes
  allowRead?: boolean; // 上傳後是否允許臨時讀取（預設否）
  ip?: string; // 可選：鎖定單一 IP
  ipRange?: string; // IP range for bulk operations
  contentType?: string;
};

export type DirectorySasInput = {
  container: string;
  directory: string;
  ttlMinutes?: number;
  ipRange?: string;
};

function normalizeBlobPath(p: string) {
  // 1) 正規化分隔符與移除前置斜線
  let s = p.replace(/\\/g, "/").replace(/\/{2,}/g, "/").replace(/^\/+/, "");
  // 2) 移除 ASCII 控制字元
  s = s.replace(/[\u0000-\u001F\u007F]/g, "");
  // 3) 驗證每個片段：不得為 "."、".."，且不得以 "." 結尾（依 Azure 建議）
  const parts = s.split("/").filter(Boolean);
  if (parts.length === 0) throw new Error("Invalid blob path");
  const out: string[] = [];
  for (const raw of parts) {
    if (raw === "." || raw === "..") throw new Error("Invalid blob path");
    const seg = raw.trim();
    if (!seg || /\.$/.test(seg)) throw new Error("Invalid blob path");
    out.push(seg);
  }
  return out.join("/");
}

function encodePathSegments(path: string) {
  // 逐段編碼，保留路徑層級的斜線
  return path.split("/").map(encodeURIComponent).join("/");
}

export function getQueueClient(): QueueClient {
  const queueCredential = new QueueStorageSharedKeyCredential(account, accountKey);
  return new QueueClient(`https://${account}.queue.core.windows.net/${queueName}`, queueCredential);
}

// export function getSasForBlob(blobName: string, minutes = Number(process.env.SAS_TTL_MINUTES ?? 15)) {
//   const startsOn = new Date(Date.now() - 60 * 1000);
//   const expiresOn = new Date(Date.now() + minutes * 60 * 1000);
//   const sas = generateBlobSASQueryParameters(
//     { containerName, blobName, permissions: BlobSASPermissions.parse("cw"), startsOn, expiresOn },
//     credential
//   ).toString();
//   const url = `https://${account}.blob.core.windows.net/${containerName}/${encodeURI(blobName)}?${sas}`;
//   return { sas, url, expiresOn };
// }

export async function issueUploadSas({ 
  blob, 
  blobName, 
  container, 
  size, 
  ttlSeconds, 
  ttlMinutes, 
  allowRead, 
  ip, 
  ipRange, 
  contentType 
}: UploadSasInput) {
  const finalBlobName = normalizeBlobPath(blob || blobName || "");
  const finalContainer = container || containerName;
  const now = new Date();
  
  // Calculate TTL - support both seconds and minutes
  let ttl: number;
  if (ttlMinutes) {
    ttl = ttlMinutes * 60;
  } else if (ttlSeconds) {
    ttl = ttlSeconds;
  } else {
    ttl = size && size > 100 * 1024 * 1024 ? 45 * 60 : 15 * 60; // 大檔 45m、小檔 15m
  }
  
  const startsOn = new Date(now.getTime() - 60 * 1000);
  const expiresOn = new Date(now.getTime() + ttl * 1000);

  const perms = BlobSASPermissions.parse(allowRead ? "rcw" : "cw");
  const cred = new StorageSharedKeyCredential(account, accountKey);
  
  // Handle IP range
  let ipRangeObj: { start: string; end: string } | undefined;
  const ipToUse = ipRange || ip;
  if (ipToUse) {
    const [start, end] = ipToUse.split("-");
    ipRangeObj = { start, end: end ?? start };
  }
  
  const sas = generateBlobSASQueryParameters({
    containerName: finalContainer,
    blobName: finalBlobName,
    permissions: perms,
    protocol: SASProtocol.Https,
    startsOn,
    expiresOn,
    ipRange: ipRangeObj,
    contentType,
  }, cred).toString();

  const url = `https://${account}.blob.core.windows.net/${finalContainer}/${encodePathSegments(finalBlobName)}?${sas}`;
  return { url, expiresOn: expiresOn.toISOString() };
}

export async function issueDirectorySas({
  container,
  directory,
  ttlMinutes = 30,
  ipRange
}: DirectorySasInput) {
  const normalizedDirectory = normalizeBlobPath(directory);
  const now = new Date();
  const startsOn = new Date(now.getTime() - 60 * 1000);
  const expiresOn = new Date(now.getTime() + ttlMinutes * 60 * 1000);
  
  // Handle IP range
  let ipRangeObj: { start: string; end: string } | undefined;
  if (ipRange) {
    const [start, end] = ipRange.split("-");
    ipRangeObj = { start, end: end ?? start };
  }

  try {
    // Use proper DataLake API for directory SAS with signedDirectoryDepth (patch requirement)
    // Calculate directory depth for signedDirectoryDepth parameter
    const depth = normalizedDirectory.split('/').length + 10; // Allow deep nesting

    const sas: DataLakeSASSignatureValues = {
      protocol: SASProtocol.Https,
      fileSystemName: container,
      pathName: normalizedDirectory,
      isDirectory: true,
      permissions: DataLakeSASPermissions.parse("rwlac"), // read, write, list, add, create
      startsOn,
      expiresOn,
      ipRange: ipRangeObj,
      // @ts-expect-error - signedDirectoryDepth is required but not in types yet
      signedDirectoryDepth: depth,
    };

    const sasToken = generateDataLakeSASQueryParameters(sas, credential).toString();
    const url = `https://${account}.dfs.core.windows.net/${container}/${encodePathSegments(normalizedDirectory)}?${sasToken}`;
    return url;
  } catch (error: any) {
    // Check if this is an HNS-related error
    if (error?.message?.includes('hierarchical') || 
        error?.message?.includes('HNS') ||
        error?.message?.includes('directory SAS not supported')) {
      throw new Error('Directory-scoped SAS requires hierarchical namespace (ADLS Gen2) to be enabled');
    }
    throw error;
  }
}

export function getBlobBatchClient() {
  // JS SDK constructor accepts service URL + credential
  return new BlobBatchClient(blobService.url, credential);
}