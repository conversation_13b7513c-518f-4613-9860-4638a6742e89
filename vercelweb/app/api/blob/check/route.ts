import { NextRequest, NextResponse } from "next/server";
import { BlobServiceClient, StorageSharedKeyCredential } from "@azure/storage-blob";

const account = process.env.AZURE_STORAGE_ACCOUNT!;
const key = process.env.AZURE_STORAGE_ACCOUNT_KEY!;
const container = process.env.AZURE_BLOB_CONTAINER || "trial";

export async function POST(req: NextRequest) {
  try {
    const { path, sha256 } = await req.json();
    
    if (!path) {
      return NextResponse.json({ error: "Missing path parameter" }, { status: 400 });
    }

    const cred = new StorageSharedKeyCredential(account, key);
    const svc = new BlobServiceClient(`https://${account}.blob.core.windows.net`, cred);
    const c = svc.getContainerClient(container);
    const blob = c.getBlobClient(path);

    if (!(await blob.exists())) {
      return NextResponse.json({ exists: false });
    }

    const props = await blob.getProperties();
    let tags: Record<string, string> | undefined;
    try { 
      tags = (await blob.getTags()).tags; 
    } catch {
      // Tags may not be enabled or accessible
    }
    
    const same = tags?.sha256 && sha256 && tags.sha256 === sha256;

    return NextResponse.json({
      exists: true,
      etag: props.etag,
      sameContent: !!same,
      tags
    });
  } catch (error) {
    console.error("Blob check error:", error);
    return NextResponse.json(
      { error: "Failed to check blob" }, 
      { status: 500 }
    );
  }
}