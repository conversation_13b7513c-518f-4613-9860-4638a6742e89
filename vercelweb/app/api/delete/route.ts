import { NextRequest, NextResponse } from "next/server";
import { getContainerClient, getBlobBatchClient } from "@/server/azure";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const preferredRegion = ['hkg1'];

export async function POST(req: NextRequest) {
  const { items }: { items: { path: string; isDir?: boolean }[] } = await req.json();
  if (!items?.length) return NextResponse.json({ ok: true, total: 0, succeeded: [], failed: [] });
  
  const container = getContainerClient();
  const batch = getBlobBatchClient();
  const targets: string[] = [];
  
  for (const it of items) {
    if (it.isDir || it.path.endsWith("/")) {
      for await (const b of container.listBlobsFlat({ prefix: it.path })) targets.push(b.name);
    } else targets.push(it.path);
  }
  
  const succeeded: string[] = [];
  const failed: { path: string; status: number; errorCode?: string; message?: string }[] = [];
  const CHUNK = 256;
  
  for (let i = 0; i < targets.length; i += CHUNK) {
    const nameSlice = targets.slice(i, i + CHUNK);
    const clients = nameSlice.map((name) => container.getBlobClient(name));
    const resp = await batch.deleteBlobs(clients, { deleteSnapshots: "include" });
    const subs = (resp as any).subResponses as Array<{ status: number; statusMessage: string; errorCode?: string }>;
    subs.forEach((sr, j) => { const name = nameSlice[j]; if (sr.status >= 200 && sr.status < 300) succeeded.push(name); else failed.push({ path: name, status: sr.status, errorCode: sr.errorCode, message: sr.statusMessage }); });
  }
  
  return NextResponse.json({ ok: failed.length === 0, total: targets.length, succeeded, failed });
}