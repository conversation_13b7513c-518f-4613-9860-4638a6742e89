import { NextRequest, NextResponse } from "next/server";
import { getQueueClient } from "@/server/azure";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const preferredRegion = ['hkg1'];

export async function POST(req: NextRequest) {
  const body = await req.text();
  const queue = getQueueClient();
  await queue.createIfNotExists();
  await queue.sendMessage(body);
  return NextResponse.json({ ok: true });
}