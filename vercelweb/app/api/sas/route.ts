import { NextRequest, NextResponse } from "next/server";
import { issueUploadSas } from "@/server/azure";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const preferredRegion = ['hkg1'];
export const maxDuration = 15; // increase per plan if needed

function okJson(data: any) { const res = NextResponse.json(data); res.headers.set('Cache-Control', 'no-store'); return res; }

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const blob = searchParams.get("blob")?.trim();
    if (!blob) return okJson({ error: "missing blob" });

    const size = Math.max(0, Number(searchParams.get("size") || 0));
    const contentType = searchParams.get("contentType") || undefined;

    // Guardrails: soft upper bound to avoid huge free‑tier uploads over SAS
    const MAX = Number(process.env.NEXT_PUBLIC_MAX_UPLOAD || 1024 * 1024 * 1024); // default 1 GB
    if (size > MAX) return NextResponse.json({ error: `file too large (>${MAX} bytes)` }, { status: 413 });

    // Optional: pin SAS to caller IP (behind proxy)
    // const ip = req.headers.get("x-forwarded-for")?.split(",")[0].trim();
    const result = await issueUploadSas({ blob, size, contentType /*, ip*/ });

    return okJson({ url: result.url, expiresOn: result.expiresOn });
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Bad Request' }, { status: 400 });
  }
}