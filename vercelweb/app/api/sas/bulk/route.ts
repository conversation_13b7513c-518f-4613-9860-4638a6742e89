import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { issueUploadSas, issueDirectorySas, getContainerClient } from '@/server/azure';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// ---- Guards & policy ----
const ALLOW_CONTAINERS = new Set<string>(['trial']);
const MAX_BLOBS_PER_REQ = 500;
const MAX_DEPTH = 32;
const MAX_NAME_BYTES = 1024; // enforce by bytes
const MAX_SEGMENT_BYTES = 255;

// In-memory (best-effort) idempotency cache (serverless note: not cross-instance)
const IDEMPOTENCY_TTL_MS = 10 * 60 * 1000;
const idemCache = new Map<string, { expires: number; response: any }>();

const BodySchema = z.object({
  container: z.string().min(1),
  blobs: z.array(z.object({ name: z.string().min(1), contentType: z.string().optional() })).optional(),
  directory: z.string().optional(),
  ttlMinutes: z.number().int().min(5).max(180).default(30),
  ipRange: z.string().optional(),
});

function err(code: string, message: string, status = 400) {
  return NextResponse.json({ error: { code, message } }, { status });
}

function byteLen(s: string) { return new TextEncoder().encode(s).length; }

function normalizePath(p: string) {
  // normalize slashes, remove control chars and leading '/'
  let s = p.replace(/\\/g, '/').replace(/\/{2,}/g, '/').replace(/^\/+/, '');
  s = s.replace(/[\x00-\x1F\x7F]/g, '');
  const parts = s.split('/').filter(Boolean);
  if (parts.length === 0) throw new Error('EMPTY');
  if (parts.length > MAX_DEPTH) throw new Error('DEPTH');
  const out: string[] = [];
  for (const raw of parts) {
    if (raw === '.' || raw === '..') throw new Error('DOT');
    const seg = raw.trim().replace(/\.$/, '_');
    if (!seg) throw new Error('SEGMENT');
    if (byteLen(seg) > MAX_SEGMENT_BYTES) throw new Error('SEGMENT_LEN');
    out.push(seg);
  }
  const joined = out.join('/');
  if (byteLen(joined) > MAX_NAME_BYTES) throw new Error('NAME_LEN');
  return joined;
}

function ipFrom(req: NextRequest) {
  return req.headers.get('x-forwarded-for')?.split(',')[0].trim() || '0.0.0.0';
}

// naive token bucket per IP (best-effort)
const rateMap = new Map<string, { tokens: number; ts: number }>();
function rateLimit(ip: string, limit = 60, refillPerSec = 1) { // 60 tokens, +1/s
  const now = Date.now();
  const state = rateMap.get(ip) || { tokens: limit, ts: now };
  const elapsed = (now - state.ts) / 1000;
  state.tokens = Math.min(limit, state.tokens + elapsed * refillPerSec);
  state.ts = now;
  if (state.tokens < 1) return false;
  state.tokens -= 1; rateMap.set(ip, state); return true;
}

export async function POST(req: NextRequest) {
  const ip = ipFrom(req);
  if (!rateLimit(ip)) return err('RATE_LIMIT', 'Too many requests', 429);

  // Idempotency (best-effort)
  const idem = req.headers.get('idempotency-key') || '';
  if (idem) {
    const cached = idemCache.get(idem);
    if (cached && cached.expires > Date.now()) {
      return NextResponse.json({ ...cached.response, idempotencyKey: idem });
    }
  }

  try {
    const body = await req.json();
    const { container, blobs, directory, ttlMinutes, ipRange } = BodySchema.parse(body);

    if (!ALLOW_CONTAINERS.has(container)) return err('CONTAINER_NOT_ALLOWED', `container '${container}' is not allowed`);
    if (!blobs && !directory) return err('MISSING_INPUT', 'Either blobs[] or directory is required');

    // Directory SAS（需啟用 HNS）
    if (directory) {
      try {
        const dir = normalizePath(directory);
        const sas = await issueDirectorySas({ container, directory: dir, ttlMinutes, ipRange });
        const expiresInSeconds = ttlMinutes * 60;
        const res = { kind: 'directory', sas, name: dir, expiresInSeconds };
        if (idem) idemCache.set(idem, { expires: Date.now() + IDEMPOTENCY_TTL_MS, response: res });
        return NextResponse.json(res);
      } catch (e: any) {
        const msg = String(e?.message || e);
        if (/HNS|hierarchical|directory\s*SAS\s*not\s*supported/i.test(msg)) {
          return err('HNS_REQUIRED', 'Directory-scoped SAS requires hierarchical namespace (ADLS Gen2) to be enabled', 400);
        }
        return err('DIRECTORY_SAS_ERROR', msg, 400);
      }
    }

    // Per-file SAS（部分成功）
    const list = (blobs ?? []).slice(0, MAX_BLOBS_PER_REQ);
    const expiresInSeconds = ttlMinutes * 60;

    const containerClient = getContainerClient();
    const items = await Promise.all(list.map(async (b) => {
      try {
        const name = normalizePath(b.name);
        const { url, expiresOn } = await issueUploadSas({
          container,
          blobName: name,
          contentType: b.contentType,
          ipRange,
          ttlMinutes,
        });

        // Check blob existence, etag, and tags for deduplication (as per patch)
        const blob = containerClient.getBlobClient(name);
        const exists = await blob.exists();
        let etag: string | undefined;
        let tags: Record<string, string> | undefined;

        if (exists) {
          const props = await blob.getProperties();
          etag = props.etag;
          try {
            const r = await blob.getTags();
            tags = r.tags;
          } catch {
            // Tags may not be enabled or accessible
          }
        }

        return { 
          name, 
          nameNormalized: name, 
          url, 
          expiresOn, 
          expiresInSeconds,
          exists,
          etag,
          tags
        };
      } catch (e: any) {
        return { name: b.name, error: { code: 'SAS_ERROR', message: String(e?.message || e) } };
      }
    }));

    const res = { kind: 'blobs', items, idempotencyKey: idem || undefined };
    if (idem) idemCache.set(idem, { expires: Date.now() + IDEMPOTENCY_TTL_MS, response: res });
    return NextResponse.json(res);
  } catch (err: any) {
    return err('BAD_REQUEST', err?.message ?? 'Unknown error', 400);
  }
}