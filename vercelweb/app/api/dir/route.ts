import { NextRequest, NextResponse } from "next/server";
import { getContainerClient } from "@/server/azure";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const preferredRegion = ['hkg1'];

export async function GET(req: NextRequest) {
  const url = new URL(req.url);
  const path = url.searchParams.get("path") ?? "";
  const limit = Number(url.searchParams.get("limit") ?? process.env.DIR_PAGE_SIZE ?? 200);
  const marker = url.searchParams.get("marker") ?? undefined;
  
  const container = getContainerClient();
  const iter = container.listBlobsByHierarchy("/", { prefix: path }).byPage({ continuationToken: marker, maxPageSize: limit });
  const items: { type: "dir"|"file"; name: string; path: string; size?: number; lastModified?: string }[] = [];
  let nextMarker: string | null = null;
  
  for await (const page of iter) {
    nextMarker = (page as any).continuationToken ?? null;
    const seg = (page as any).segment;
    for (const p of seg?.blobPrefixes ?? []) items.push({ type: "dir", name: p.name.slice(path.length).replace(/\/$/,""), path: p.name });
    for (const b of seg?.blobItems ?? []) items.push({ type: "file", name: b.name.slice(path.length), path: b.name, size: Number(b.properties?.contentLength ?? 0), lastModified: (b.properties?.lastModified instanceof Date ? b.properties.lastModified : new Date()).toISOString() });
    break;
  }
  
  return NextResponse.json({ path, items, nextMarker });
}