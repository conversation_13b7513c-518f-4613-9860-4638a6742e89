import { NextRequest } from "next/server";
import { getContainerClient } from "@/server/azure";
import archiver from "archiver";
import { PassThrough, Readable as NodeReadable } from "stream";

export const runtime = 'nodejs';
export const dynamic = "force-dynamic";
export const preferredRegion = ['hkg1'];

export async function POST(req: NextRequest) {
  const { items }: { items: { path: string; isDir?: boolean }[] } = await req.json();
  if (!items?.length) return new Response("No items", { status: 400 });
  
  const container = getContainerClient();
  const entries: { blobName: string; zipName: string }[] = [];
  
  for (const it of items) {
    if (it.isDir || it.path.endsWith("/")) {
      for await (const b of container.listBlobsFlat({ prefix: it.path })) entries.push({ blobName: b.name, zipName: b.name });
    } else entries.push({ blobName: it.path, zipName: it.path });
  }
  
  const CONCURRENCY = Math.max(1, Number(process.env.ZIP_CONCURRENCY ?? 6));
  const errors: { blobName: string; reason: string }[] = [];
  const VALIDATE = process.env.ZIP_VALIDATE === '1';
  let ok = [] as typeof entries;
  
  if (!VALIDATE || entries.length > 2000) {
    ok = entries;
  } else {
    let cursor = 0;
    async function preflightWorker() {
      while (cursor < entries.length) {
        const idx = cursor++; const e = entries[idx];
        try { await container.getBlobClient(e.blobName).getProperties(); ok.push(e); }
        catch (err:any) { errors.push({ blobName: e.blobName, reason: (err?.details?.errorCode||err?.message||"unknown error").toString() }); }
      }
    }
    await Promise.all(Array.from({ length: Math.min(CONCURRENCY, entries.length) }, preflightWorker));
  }
  
  const pass = new PassThrough();
  const archive = archiver("zip", { zlib: { level: 9 } });
  archive.on("warning", (e) => console.warn("zip warning", e));
  archive.on("error", (e) => pass.destroy(e));
  archive.pipe(pass);
  
  if (errors.length) archive.append(Buffer.from(JSON.stringify({ failed: errors }, null, 2)), { name: "_zip_errors.json" });
  
  let dCursor = 0;
  async function downloadWorker() {
    while (dCursor < ok.length) {
      const idx = dCursor++; const e = ok[idx];
      try { const resp = await container.getBlobClient(e.blobName).download(); archive.append(resp.readableStreamBody as any, { name: e.zipName }); }
      catch { archive.append(Buffer.from(`download error: ${e.blobName}`), { name: `errors/${e.zipName}.ERROR.txt` }); }
    }
  }
  
  (async () => { try { await Promise.all(Array.from({ length: Math.min(CONCURRENCY, ok.length) }, downloadWorker)); await archive.finalize(); } catch (err) { pass.destroy(err as any); } })();
  
  return new Response(NodeReadable.toWeb(pass) as any, { headers: { "Content-Type": "application/zip", "Content-Disposition": `attachment; filename=download_${Date.now()}.zip`, "X-Zip-Error-Count": String(errors.length) } });
}