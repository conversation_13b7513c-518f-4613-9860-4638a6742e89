"use client";
import { useState } from "react";
import { FileExplorer } from "@/app/components/FileExplorer";
import { EmbeddingPoolPanel } from "@/app/components/EmbeddingPool";
import { UploadPanel } from "@/app/components/UploadPanel";

export default function Page() {
  const [prefix, setPrefix] = useState<string>(process.env.NEXT_PUBLIC_DEFAULT_PREFIX ?? "");
  return (
    <div className="grid grid-cols-1 lg:grid-cols-[1fr_320px] gap-4">
      <div className="space-y-4">
        <UploadPanel currentPrefix={prefix} />
        <FileExplorer prefix={prefix} onChange={setPrefix} />
      </div>
      <EmbeddingPoolPanel />
    </div>
  );
}