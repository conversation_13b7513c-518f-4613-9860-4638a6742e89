"use client";
import { useEffect, useMemo, useRef, useState, type DragEvent } from "react";
import useS<PERSON> from "swr";
import { Virtuoso } from "react-virtuoso";
import { bus } from "@/lib/eventBus";
import { formatBytes, parseBytes } from "@/lib/bytes";
import { useEmbeddingPool } from "./EmbeddingPool";

const fetcher = (url: string) => fetch(url).then((r) => r.json());
type ApiResp = { path: string; items: ({ type: 'dir'|'file'; name: string; path: string; size?: number; lastModified?: string })[]; nextMarker: string|null };
type DirItem = { name: string; path: string };
type FileItem = { name: string; path: string; size: number; lastModified: string };

// Security and utility functions from patch
function normalizeRelPath(p: string): string {
  let s = p.replace(/\\/g, "/").replace(/\/{2,}/g, "/").replace(/^\/+/, "");
  s = s.replace(/[\u0000-\u001F\u007F]/g, ""); // 控制字元
  const parts = s.split("/").filter(Boolean);
  const out: string[] = [];
  for (const part of parts) {
    if (part === "." || part === "..") continue;
    let seg = part.trim();
    seg = seg.replace(/\.$/, "_"); // 片段末尾 "." 改為 "_"
    if (seg) out.push(seg);
  }
  return out.join("/");
}

function ensureUnique(name: string, used: Set<string>): string {
  if (!used.has(name)) { used.add(name); return name; }
  const m = /^(.*?)(?:\((\d+)\))?(\.[^.]*)?$/.exec(name);
  const base = (m?.[1] ?? name).replace(/\s+$/, "");
  const ext = m?.[3] ?? ""; let i = Number(m?.[2] || 1);
  let candidate = `${base} (${i})${ext}`;
  while (used.has(candidate)) { i++; candidate = `${base} (${i})${ext}`; }
  used.add(candidate); return candidate;
}

export function FileExplorer({ prefix, onChange }: { prefix: string; onChange: (p: string) => void }) {
  const [marker, setMarker] = useState<string | null>(null);
  const [items, setItems] = useState<((DirItem & { kind: "dir" }) | (FileItem & { kind: "file" }))[]>([]);
  const [selected, setSelected] = useState<Record<string, { isDir?: boolean; size?: number }>>({});
  const [dndOver, setDndOver] = useState(false);
  const [summary, setSummary] = useState<{ count: number; bytes: number; errors: number } | null>(null);
  const [guardMsg, setGuardMsg] = useState<string | null>(null);
  const prevPrefix = useRef<string>(prefix);
  const abortRef = useRef<AbortController | null>(null);
  const { add } = useEmbeddingPool();
  
  // Guard rails (可用環境變數覆寫)
  const MAX_FILES = Number(process.env.NEXT_PUBLIC_MAX_FILES ?? 1000);
  const MAX_TOTAL = parseBytes(process.env.NEXT_PUBLIC_MAX_TOTAL ?? "2 GB");
  const PAGE_SIZE = Number(process.env.NEXT_PUBLIC_DIR_PAGE_SIZE ?? 200);
  const { data, mutate } = useSWR<ApiResp>(`/api/dir?path=${encodeURIComponent(prefix)}${marker ? `&marker=${marker}` : ""}&limit=${PAGE_SIZE}`, fetcher, { revalidateOnFocus: false, keepPreviousData: true });
  
  useEffect(() => { 
    if (prevPrefix.current !== prefix) { 
      setMarker(null); 
      setItems([]); 
      setSelected({}); 
      setSummary(null); 
      setGuardMsg(null); 
      prevPrefix.current = prefix; 
    } 
  }, [prefix]);
  useEffect(() => { if (!data) return; const page = [
    ...data.items.filter((it:any)=>it.type==="dir").map((d:any) => ({ name: d.name, path: d.path, kind: "dir" as const })),
    ...data.items.filter((it:any)=>it.type==="file").map((f:any) => ({ name: f.name, path: f.path, size: f.size ?? 0, lastModified: f.lastModified ?? new Date().toISOString(), kind: "file" as const }))
  ]; setItems((prev) => (marker ? [...prev, ...page] : page)); }, [data, marker]);
  useEffect(() => { const h = (e: { prefix: string }) => { if (e.prefix === prefix) mutate(); }; bus.on("upload:done", h); return () => bus.off("upload:done", h as any); }, [prefix, mutate]);
  
  function toggle(path: string, meta: { isDir?: boolean; size?: number }) {
    setSelected((s) => { 
      if (s[path]) { 
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { [path]: _, ...rest } = s; 
        return rest; 
      } 
      return { ...s, [path]: meta }; 
    });
  }
  function clearSelection() { setSelected({}); }
  function selectAll() {
    setSelected(() => {
      const next: Record<string, { isDir?: boolean; size?: number }> = {};
      for (const it of items) { if (it.kind === "dir") next[it.path] = { isDir: true }; else next[it.path] = { size: it.size }; }
      return next;
    });
  }
  function deselectAll() { clearSelection(); }

  function handleDragStart(ev: DragEvent, item: { path: string; size?: number; isDir?: boolean }) {
    try {
      const isMulti = !!selected[item.path];
      const list = isMulti ? Object.entries(selected).map(([p, m]) => (m?.isDir ? { path: p, isDir: true } : { path: p, size: m?.size })) : [item];
      const payload = JSON.stringify(list);
      ev.dataTransfer.setData("application/x-embedding-items", payload);
      ev.dataTransfer.setData("text/plain", payload);
      ev.dataTransfer.effectAllowed = "copy";
    } catch {}
  }

  // Enhanced upload logic with security and error handling from patch
  function onDragOver(e: DragEvent) {
    // Only intercept real file drags (not our internal drag payloads)
    const types = Array.from(e.dataTransfer?.types || []);
    if (types.includes("Files")) { e.preventDefault(); setDndOver(true); }
  }
  function onDragLeave() { setDndOver(false); }
  
  async function onDrop(e: DragEvent) {
    e.preventDefault(); setDndOver(false);
    const controller = new AbortController(); abortRef.current = controller;
    const { signal } = controller;

    const files: Array<{ file: File; relPath?: string }> = [];
    const used = new Set<string>();
    let total = 0; let errors = 0;

    const items = Array.from(e.dataTransfer.items || []);
    const toEntry = (it: DataTransferItem) => (it as any).webkitGetAsEntry?.() as FileSystemEntry | null;

    async function walk(entry: FileSystemEntry, base = ""): Promise<void> {
      if (signal.aborted) throw new Error("aborted");
      if (entry.isFile) {
        await new Promise<void>((resolve) => (entry as FileSystemFileEntry).file((f) => {
          const rel = base ? `${base}/${f.name}` : f.name;
          const normalized = normalizeRelPath(rel);
          const unique = ensureUnique(normalized, used);
          if (files.length + 1 > MAX_FILES) { 
            errors++; 
            setGuardMsg(`超過最大檔數 ${MAX_FILES}`);
            return resolve(); 
          }
          if (total + f.size > MAX_TOTAL) { 
            errors++; 
            setGuardMsg(`超過最大總容量 ${formatBytes(MAX_TOTAL)}`);
            return resolve(); 
          }
          total += f.size; files.push({ file: f, relPath: unique }); resolve();
        }));
      } else if (entry.isDirectory) {
        const dir = entry as FileSystemDirectoryEntry; const reader = dir.createReader();
        const readBatch = (): Promise<FileSystemEntry[]> => new Promise((r) => reader.readEntries(r));
        let batch: FileSystemEntry[] = [];
        do {
          if (signal.aborted) throw new Error("aborted");
          batch = await readBatch();
          for (const child of batch) await walk(child, base ? `${base}/${dir.name}` : dir.name);
        } while (batch.length);
      }
    }

    const entries = items.map(toEntry).filter(Boolean) as FileSystemEntry[];
    try { 
      for (const ent of entries) await walk(ent!); 
    } catch (err) { 
      if ((err as any).message !== "aborted") console.error(err); 
    } finally { 
      abortRef.current = null; 
    }

    if (files.length) {
      bus.emit("upload:request", { prefix, entries: files });
      setSummary({ count: files.length, bytes: total, errors });
    }
  }
  
  function cancelScan() { 
    abortRef.current?.abort(); 
    setGuardMsg("掃描已取消");
  }

  async function doZip() {
    const payload = Object.entries(selected).map(([path, m]) => ({ path, isDir: m?.isDir }));
    const res = await fetch(`/api/zip?path=${encodeURIComponent(prefix)}`, { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ items: payload }) });
    try {
      const blob = await res.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a"); a.href = url; a.download = `download-${Date.now()}.zip`; a.click(); URL.revokeObjectURL(url);
    } catch {}
  }
  async function doDelete() {
    const payload = Object.entries(selected).map(([path, m]) => ({ path, isDir: m?.isDir }));
    const res = await fetch("/api/delete", { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ items: payload }) });
    clearSelection();
    try { const j = await res.json(); if (j?.failed?.length) alert(`刪除完成，但有 ${j.failed.length} 項失敗`); } catch {}
    mutate();
  }
  function doAddToPool() { for (const [path, meta] of Object.entries(selected)) add({ path, size: meta?.size }); clearSelection(); }

  const toolbar = (
    <div className="flex gap-2 items-center">
      <button className="btn" onClick={selectAll}>全選</button>
      <button className="btn" onClick={deselectAll} disabled={!Object.keys(selected).length}>全不選</button>
      <div className="w-px h-6 bg-neutral-200 mx-1" />
      <button className="btn" disabled={!Object.keys(selected).length} onClick={doDelete}>刪除</button>
      <button className="btn" disabled={!Object.keys(selected).length} onClick={doZip}>下載為 ZIP</button>
      <button className="btn" disabled={!Object.keys(selected).length} onClick={doAddToPool}>加入 Embedding Pool</button>
      {abortRef.current && (
        <button className="btn btn-warning" onClick={cancelScan}>取消掃描</button>
      )}
      {data?.nextMarker && (<button className="btn" onClick={() => setMarker(data.nextMarker!)}>載入更多</button>)}
    </div>
  );

  return (
    <div className={`bg-white rounded-2xl p-4 shadow-sm border ${dndOver ? "border-blue-400 ring-2 ring-blue-100" : "border-neutral-200"}`}
         onDragOver={onDragOver} onDragLeave={onDragLeave} onDrop={onDrop}>
      <div className="flex items-center justify-between"><Breadcrumb prefix={prefix} onJump={onChange} />{toolbar}</div>
      
      {/* Upload status messages */}
      {guardMsg && (
        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 text-sm">
          ⚠️ {guardMsg} 
          <button className="ml-2 text-yellow-600 hover:text-yellow-800" onClick={() => setGuardMsg(null)}>✕</button>
        </div>
      )}
      
      {summary && (
        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg text-green-800 text-sm">
          ✅ 已上傳 {summary.count} 個檔案，總大小 {formatBytes(summary.bytes)}
          {summary.errors > 0 && <span className="text-orange-600"> ({summary.errors} 個檔案被跳過)</span>}
          <button className="ml-2 text-green-600 hover:text-green-800" onClick={() => setSummary(null)}>✕</button>
        </div>
      )}
      
      {dndOver && (
        <div className="mt-3 p-4 border-2 border-dashed rounded-xl text-center text-neutral-600">
          拖放檔案或資料夾以上傳到：<b>{prefix || "根目錄"}</b>
          <div className="text-xs text-neutral-500 mt-1">
            限制：最多 {MAX_FILES} 個檔案，總計 {formatBytes(MAX_TOTAL)}
          </div>
        </div>
      )}
      
      <div className="mt-3 border rounded-xl overflow-hidden" style={{ height: 480 }}>
        <Virtuoso data={items} itemContent={(_, it) => it.kind === "dir" ? (
          <RowDir key={it.path} dir={it}
            onOpen={() => { setMarker(null); setItems([]); onChange(it.path); }}
            onToggle={() => toggle(it.path, { isDir: true })}
            onDragStart={(ev) => handleDragStart(ev, { path: it.path, isDir: true })}
            checked={!!selected[it.path]} />
        ) : (
          <RowFile key={it.path} file={it}
            onToggle={() => toggle(it.path, { size: it.size })}
            onDragStart={(ev) => handleDragStart(ev, { path: it.path, size: it.size })}
            checked={!!selected[it.path]} />
        )} />
      </div>
    </div>
  );
}

function Breadcrumb({ prefix, onJump }: { prefix: string; onJump: (p: string) => void }) {
  const parts = useMemo(() => { 
    const p = prefix.replace(/\/$/, ""); 
    if (!p) return [{ name: "根目錄" as const, path: "" }]; 
    const segs = p.split("/"); 
    const out: Array<{ name: string; path: string }> = [{ name: "根目錄", path: "" }]; 
    let cur = ""; 
    for (const s of segs) { 
      cur += s + "/"; 
      out.push({ name: s, path: cur }); 
    } 
    return out; 
  }, [prefix]);
  return (
    <nav className="text-base md:text-lg font-medium text-neutral-800">
      {parts.map((p, i) => (
        <span key={p.path} className="inline-flex items-center">
          <button className="px-2 py-1 rounded-md hover:bg-neutral-100" onClick={() => onJump(p.path)}>
            {p.name || "(空)"}
          </button>
          {i < parts.length - 1 && <span className="mx-1 text-neutral-400">/</span>}
        </span>
      ))}
    </nav>
  );
}
function RowDir({ dir, onOpen, onToggle, checked, onDragStart }: { dir: DirItem; onOpen: () => void; onToggle: () => void; checked: boolean; onDragStart?: (e: DragEvent<HTMLDivElement>) => void }) {
  return (
    <div className={`flex items-center gap-3 px-3 py-2 border-b ${checked ? "bg-blue-50" : "hover:bg-neutral-50"}`} draggable onDragStart={onDragStart}
      onClick={(e) => {
        const target = e.target as HTMLElement;
        if (target.closest('button')) return;
        onToggle();
      }}>
      <input type="checkbox" checked={checked} onChange={onToggle} />
      <button className="text-blue-700 hover:underline font-medium" onClick={onOpen}>📁 {dir.name || "(資料夾)"}</button>
      <div className="ml-auto text-xs text-neutral-500">資料夾</div>
    </div>
  );
}
function RowFile({ file, onToggle, checked, onDragStart }: { file: FileItem; onToggle: () => void; checked: boolean; onDragStart?: (e: DragEvent<HTMLDivElement>) => void }) {
  const date = new Intl.DateTimeFormat("zh-HK", { timeZone: "Asia/Hong_Kong", dateStyle: "medium", timeStyle: "short" }).format(new Date(file.lastModified));
  return (
    <div className={`flex items-center gap-3 px-3 py-2 border-b ${checked ? "bg-blue-50" : "hover:bg-neutral-50"}`} draggable onDragStart={onDragStart}
      onClick={(e) => {
        const target = e.target as HTMLElement;
        if (target.closest('button')) return;
        onToggle();
      }}>
      <input type="checkbox" checked={checked} onChange={onToggle} />
      <div className="truncate">🗎 {file.name}</div>
      <div className="ml-auto text-xs text-neutral-500">{formatBytes(file.size)} · {date}</div>
    </div>
  );
}