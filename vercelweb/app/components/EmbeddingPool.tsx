"use client";
import { useEffect, useMemo, useRef, useState, type DragEvent } from "react";
import { bus } from "@/lib/eventBus";
import { formatBytes } from "@/lib/bytes";

export type PoolItem = { path: string; size?: number };

export function useEmbeddingPool() {
  const [entries, setEntries] = useState<PoolItem[]>([]);
  const instanceId = useRef<string>(Math.random().toString(36).slice(2));
  // Hydrate from localStorage on mount to avoid SSR/client hydration mismatch
  useEffect(() => {
    const raw = typeof window !== "undefined" ? localStorage.getItem("embedding:pool") : null;
    setEntries(raw ? JSON.parse(raw) : []);
  }, []);
  // Persist and notify others when entries change
  useEffect(() => {
    if (typeof window === "undefined") return;
    localStorage.setItem("embedding:pool", JSON.stringify(entries));
    bus.emit("pool:changed", { source: instanceId.current });
  }, [entries]);
  // Listen for external changes
  useEffect(() => {
    const onChanged = (e?: { source?: string }) => {
      if (e?.source === instanceId.current) return; // ignore self
      const raw = localStorage.getItem("embedding:pool");
      const next = raw ? JSON.parse(raw) : [];
      setEntries((cur) => (JSON.stringify(cur) === JSON.stringify(next) ? cur : next));
    };
    bus.on("pool:changed", onChanged);
    return () => bus.off("pool:changed", onChanged as any);
  }, []);
  const add = (it: PoolItem) => setEntries((prev) => (prev.find((p) => p.path === it.path) ? prev : [...prev, it]));
  const remove = (path: string) => setEntries((prev) => prev.filter((p) => p.path !== path));
  const clear = () => setEntries([]);
  const totalBytes = useMemo(() => entries.reduce((s, e) => s + (e.size ?? 0), 0), [entries]);
  return { entries, add, remove, clear, totalBytes };
}

export function EmbeddingPoolPanel() {
  const { entries, add, remove, clear, totalBytes } = useEmbeddingPool();
  const [sending, setSending] = useState(false);
  const [over, setOver] = useState(false);

  function handleDragOver(e: DragEvent) { e.preventDefault(); setOver(true); }
  function handleDragLeave() { setOver(false); }
  function handleDrop(e: DragEvent) {
    e.preventDefault(); setOver(false);
    const raw = e.dataTransfer.getData("application/x-embedding-items") || e.dataTransfer.getData("text/plain");
    if (!raw) return;
    try {
      const items = JSON.parse(raw) as Array<{ path: string; size?: number; isDir?: boolean }>;
      items.forEach((it) => add({ path: it.path, size: it.size }));
    } catch {}
  }
  async function sendEnqueue() {
    setSending(true);
    try {
      const MAX = 48 * 1024; let batch: PoolItem[] = []; let size = 0;
      for (const it of entries) {
        const added = JSON.stringify(it); const len = new TextEncoder().encode(added).length + 2;
        if (size + len > MAX && batch.length) { await fetch("/api/embed/enqueue", { method: "POST", body: JSON.stringify(batch) }); batch = []; size = 0; }
        batch.push(it); size += len;
      }
      if (batch.length) await fetch("/api/embed/enqueue", { method: "POST", body: JSON.stringify(batch) });
      alert("已加入 Embedding 佇列");
    } finally { setSending(false); }
  }
  return (
    <aside className={`w-full lg:w-80 bg-white rounded-2xl p-4 shadow-sm border ${over ? "border-blue-400 ring-2 ring-blue-100" : "border-neutral-200"}`}
      onDragOver={handleDragOver} onDragLeave={handleDragLeave} onDrop={handleDrop}>
      <div className="flex items-baseline justify-between"><h3 className="text-lg font-semibold">Embedding Pool</h3><button className="text-sm text-neutral-600 hover:underline" onClick={clear}>清空</button></div>
      <p className="text-sm text-neutral-600 mt-1">將被向量化的檔案/資料夾：{entries.length} 項（{formatBytes(totalBytes)}）</p>
      <div className="mt-3 max-h-64 overflow-auto space-y-2">
        {entries.map((e) => (<div key={e.path} className="text-sm flex items-center justify-between bg-neutral-50 rounded p-2"><span className="truncate" title={e.path}>{e.path}</span><button className="text-xs text-red-600" onClick={() => remove(e.path)}>移除</button></div>))}
        {entries.length === 0 && <div className="text-sm text-neutral-500">尚未加入任何項目</div>}
      </div>
      <button className="btn w-full mt-3" disabled={!entries.length || sending} onClick={sendEnqueue}>加入向量化</button>
    </aside>
  );
}