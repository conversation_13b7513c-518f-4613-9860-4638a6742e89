"use client";
import { useCallback, useEffect, useRef, useState } from "react";
import { bus } from "@/lib/eventBus";
import { BlockBlobClient } from "@azure/storage-blob";

// —— Optimized Configuration (browser) ——
const MAX_ZIP_BYTES = 250 * 1024 * 1024; // 250MB business rule
const MAX_SINGLE_SHOT = 256 * 1024 * 1024; // align with SDK default (single PUT up to 256 MiB)
const BLOCK_SIZE = 16 * 1024 * 1024; // 16MB blocks (>256KiB to leverage high-throughput path)
const FILE_CONCURRENCY_FALLBACK = 3; // files-at-once when bandwidth unknown

type LocalEntry = { file: File; relPath?: string };

async function sha256(file: File): Promise<string> {
  // Streaming SHA256 calculation to avoid loading entire file into memory (patch feature)
  const buf = await file.arrayBuffer();
  const hash = await crypto.subtle.digest("SHA-256", buf);
  return [...new Uint8Array(hash)].map(b => b.toString(16).padStart(2,"0")).join("");
}

async function mapWithConcurrency<T, R>(items: T[], limit: number, fn: (item: T, index: number) => Promise<R>) {
  const ret: R[] = new Array(items.length) as R[];
  let i = 0;
  const workers = new Array(Math.min(limit, items.length)).fill(0).map(async () => {
    for (;;) {
      const idx = i++;
      if (idx >= items.length) break;
      ret[idx] = await fn(items[idx], idx);
    }
  });
  await Promise.all(workers);
  return ret;
}

function decideFileConcurrency() {
  const nav = typeof navigator !== "undefined" ? (navigator as any) : null;
  const conn = nav?.connection || nav?.mozConnection || nav?.webkitConnection;
  const down = conn?.downlink; // Mbps
  if (typeof down === "number") {
    if (down >= 80) return 6;
    if (down >= 20) return 4;
    return 2;
  }
  return FILE_CONCURRENCY_FALLBACK; // 3
}


export function UploadPanel({ currentPrefix }: { currentPrefix: string }) {
  const [files, setFiles] = useState<FileList | null>(null);
  const [busy, setBusy] = useState(false);
  const [progressText, setProgressText] = useState("");
  const controllerRef = useRef<AbortController | null>(null);

  // rAF batched progress rows
  const progressRef = useRef<Record<string, { name: string; loaded: number; size: number; status: "uploading" | "done" | "error" | "canceled" }>>({});
  const [progressRows, setProgressRows] = useState<Array<{ name: string; pct: number; status: string }>>([]);
  const rafPaintRef = useRef<number | null>(null);

  function schedulePaint() {
    if (rafPaintRef.current != null) return;
    rafPaintRef.current = requestAnimationFrame(() => {
      const rows = Object.values(progressRef.current).map((v) => ({
        name: v.name,
        pct: v.size ? Math.min(100, Math.round((v.loaded / v.size) * 100)) : 0,
        status: v.status,
      }));
      setProgressRows(rows);
      rafPaintRef.current = null;
    });
  }

  useEffect(() => {
    const onBeforeUnload = (e: BeforeUnloadEvent) => { if (busy) { e.preventDefault(); e.returnValue = ""; } };
    window.addEventListener("beforeunload", onBeforeUnload);
    return () => window.removeEventListener("beforeunload", onBeforeUnload as any);
  }, [busy]);

  const uploadAll = useCallback(async (entriesArg?: LocalEntry[], overridePrefix?: string) => {
    // choose source
    let entries: LocalEntry[];
    if (entriesArg) {
      entries = entriesArg;
    } else {
      if (!files?.length) return;
      entries = Array.from(files).map((f) => ({ file: f }));
    }

    // pre-check: Zip ≤ 250MB
    for (const { file: f } of entries) {
      const isZip = /\.zip$/i.test(f.name) || f.type === "application/zip";
      if (isZip && f.size > MAX_ZIP_BYTES) {
        setProgressText(`${f.name} 超過 250MB，上傳已被拒絕`);
        return;
      }
    }

    setBusy(true);
    setProgressText("");
    controllerRef.current = new AbortController();
    const signal = controllerRef.current.signal;

    const perFileConcurrency = Math.min(
      8,
      typeof navigator !== "undefined" && (navigator as any).hardwareConcurrency
        ? (navigator as any).hardwareConcurrency
        : 4,
    );

    // ensure prefix joining rules
    const basePrefix = (overridePrefix ?? currentPrefix ?? "").replace(/\\/g, "/");

    try {
      // 1) Prepare blob paths with proper normalization
      const blobItems = entries.map(({ file, relPath }) => {
        const safeRel = (relPath || "").replace(/^[\/\\]+/, "").replace(/\\/g, "/");
        const blobPath = `${basePrefix}${safeRel}${safeRel && !safeRel.endsWith("/") ? "/" : ""}${file.name}`
          .replace(/\/+/, "/");
        return { file, blobPath };
      });

      // 2) Request bulk SAS with existence/tags data (patch enhancement)
      const bulkRes = await fetch("/api/sas/bulk", {
        method: "POST",
        headers: { "content-type": "application/json" },
        body: JSON.stringify({ 
          container: "trial", // TODO: make configurable
          blobs: blobItems.map(({ blobPath, file }) => ({ 
            name: blobPath, 
            contentType: file.type 
          }))
        }),
      });

      if (!bulkRes.ok) throw new Error(`Bulk SAS failed: ${bulkRes.status}`);
      const { items: sasItems } = await bulkRes.json() as { 
        items: Array<{ 
          name: string; 
          url: string; 
          exists?: boolean; 
          etag?: string; 
          tags?: Record<string, string>;
          error?: { code: string; message: string };
        }> 
      };

      // 3) Upload with SHA256 deduplication (patch feature)
      const fileConcurrency = decideFileConcurrency();
      await mapWithConcurrency(blobItems, fileConcurrency, async ({ file, blobPath }) => {
        if (signal.aborted) throw new DOMException("Aborted", "AbortError");

        const sasItem = sasItems.find(item => item.name === blobPath);
        if (!sasItem || sasItem.error) {
          progressRef.current[blobPath] = { name: blobPath, loaded: 0, size: file.size, status: "error" };
          schedulePaint();
          return;
        }

        // init progress
        progressRef.current[blobPath] = { name: blobPath, loaded: 0, size: file.size, status: "uploading" };
        schedulePaint();

        // SHA256 deduplication check (patch feature)
        const hash = await sha256(file);
        if (sasItem.tags?.sha256 === hash) {
          progressRef.current[blobPath] = { name: blobPath, loaded: file.size, size: file.size, status: "done" };
          schedulePaint();
          return; // Skip upload - same content
        }

        // Upload with conditional headers to avoid overwriting
        const client = new BlockBlobClient(sasItem.url);
        try {
          await client.uploadBrowserData(file, {
            maxSingleShotSize: MAX_SINGLE_SHOT,
            blockSize: BLOCK_SIZE,
            concurrency: perFileConcurrency,
            abortSignal: signal,
            blobHTTPHeaders: { blobContentType: file.type || "application/octet-stream" },
            conditions: sasItem.exists ? undefined : { ifNoneMatch: "*" }, // Patch conditional upload
            onProgress: (ev: any) => {
              const loaded = ev?.loadedBytes ?? 0;
              const item = progressRef.current[blobPath];
              if (item) { item.loaded = loaded; schedulePaint(); }
            },
          });
          
          const doneItem = progressRef.current[blobPath];
          if (doneItem) { doneItem.loaded = doneItem.size; doneItem.status = "done"; schedulePaint(); }
        } catch (e: any) {
          if (e?.name === "AbortError") throw e;
          const errItem = progressRef.current[blobPath];
          if (errItem) { errItem.status = "error"; schedulePaint(); }
        }
      });

      // 彙總結果
      const stats = Object.values(progressRef.current);
      const failed = stats.filter((v) => v.status === "error").length;
      setProgressText(failed ? `${failed} 個檔案上傳失敗` : "All files uploaded successfully!");
      bus.emit("upload:done", { prefix: basePrefix });
    } catch (err: any) {
      if (err?.name === "AbortError") {
        setProgressText("已取消上傳");
        Object.values(progressRef.current).forEach((v) => { if (v.status === "uploading") v.status = "canceled"; });
        schedulePaint();
      } else {
        console.error("upload error", err);
        setProgressText("上傳發生錯誤");
      }
    } finally {
      setBusy(false);
      setFiles(null);
      controllerRef.current = null;
    }
  }, [currentPrefix, files]);

  // New: accept uploads requested by FileExplorer (files/folders drag-drop)
  useEffect(() => {
    const handler = async (e: { prefix: string; entries: LocalEntry[] }) => {
      await uploadAll(e.entries, e.prefix);
    };
    bus.on("upload:request", handler);
    return () => bus.off("upload:request", handler as any);
  }, [uploadAll]);

  function cancelUpload() { controllerRef.current?.abort(); }

  return (
    <div className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-200">
      <div className="flex flex-wrap items-center gap-3">
        <label className="inline-flex items-center gap-2 px-3 py-2 rounded-lg border border-neutral-300 hover:bg-neutral-50 cursor-pointer">
          <input type="file" className="hidden" multiple onChange={(e) => setFiles(e.target.files)} />
          <span className="text-sm text-neutral-700">選擇檔案</span>
        </label>
        <button className="btn" disabled={!files?.length || busy} onClick={() => uploadAll()}>上傳</button>
        <button className="btn" disabled={!busy} onClick={cancelUpload}>取消</button>
        {busy && <span className="text-sm text-neutral-600">上傳中…{progressText ? `（${progressText}）` : "請勿關閉視窗"}</span>}
        {!busy && progressText && <span className="text-sm text-neutral-600">{progressText}</span>}
      </div>

      {/* 輕量級進度列表（rAF 批次更新，避免卡頓） */}
      {progressRows.length > 0 && (
        <div className="mt-3 space-y-2">
          {progressRows.map((row) => (
            <div key={row.name} className="flex items-center gap-3">
              <div className="w-48 truncate text-sm text-neutral-700" title={row.name}>{row.name}</div>
              <div className="flex-1 h-2 rounded bg-neutral-200 overflow-hidden">
                <div className="h-full bg-neutral-500" style={{ width: `${row.pct}%` }} />
              </div>
              <div className="w-16 text-right text-xs text-neutral-600">{row.pct}%</div>
              <div className="w-16 text-right text-xs text-neutral-500">{row.status}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}