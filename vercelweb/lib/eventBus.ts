import mitt from "mitt";

export type UploadBatchEvent = {
  prefix?: string; // relative folder path from UI (for display only)
  items: { file: File; relPath: string }[];
};

export type OcrReadyEvent = {
  sourcePath: string;
  markdownPath: string;
};

export type Events = {
  // Fired when UploadPanel finishes uploading a batch under a prefix
  "upload:done": { prefix: string };
  // Synced local embedding pool changes across tabs
  "pool:changed": { source: string };
  // Ask UploadPanel to upload picked files (supports folder drag‑drop)
  "upload:request": { prefix: string; entries: Array<{ file: File; relPath?: string }> };
  // Emitted by the backend or poller when a Markdown result appears (optional hook for UI badges)
  "ocr:result"?: { source: string; blob: string; mdPath: string };
  // New batch upload event for bulk processing
  "upload:batch": UploadBatchEvent;
  // OCR processing completed event
  "ocr:ready": OcrReadyEvent;
};

export const bus = mitt<Events>();