export function formatBytes(n: number) {
  if (!n) return "0 B";
  const u = ["B", "KB", "MB", "GB", "TB"]; const i = Math.floor(Math.log(n) / Math.log(1024));
  return `${(n / Math.pow(1024, i)).toFixed(1)} ${u[i]}`;
}

export function parseBytes(input: string): number {
  // e.g. "250MB", "1.5 GB" → bytes
  const m = /^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)?$/i.exec(input.trim());
  if (!m) return NaN;
  const v = parseFloat(m[1]); const unit = (m[2] || "B").toUpperCase();
  const f = { B: 1, KB: 1024, MB: 1024 ** 2, GB: 1024 ** 3, TB: 1024 ** 4 } as const;
  return v * f[unit as keyof typeof f];
}