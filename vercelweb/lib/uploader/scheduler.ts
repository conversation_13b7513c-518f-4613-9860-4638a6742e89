import pLimit from 'p-limit';
import { BlockBlobClient } from '@azure/storage-blob';

export type FileTask = {
  file: File;
  sasUrl: string;
  onProgress?: (pct: number) => void;
  contentType?: string;
  tags?: Record<string, string>;
};

export type SchedulerOptions = {
  maxConcurrentFiles?: number; // default 6
  pickTransferOptions?: (size: number) => { blockSize: number; concurrency: number; maxSingleShotSize: number };
  afterUpload?: (t: FileTask, client: BlockBlobClient) => Promise<void>;
  abortSignal?: AbortSignal;
  maxRetries?: number; // default 3
  verifyHead?: boolean; // default true
};

const defaultPick = (size: number) => {
  if (size <= 8 * 1024 * 1024) return { blockSize: 4 * 1024 * 1024, concurrency: 2, maxSingleShotSize: 8 * 1024 * 1024 };
  if (size <= 256 * 1024 * 1024) return { blockSize: 8 * 1024 * 1024, concurrency: 4, maxSingleShotSize: 16 * 1024 * 1024 };
  return { blockSize: 16 * 1024 * 1024, concurrency: 8, maxSingleShotSize: 16 * 1024 * 1024 };
};

function bucketsBySize(tasks: FileTask[]) {
  const small: FileTask[] = [], medium: FileTask[] = [], large: FileTask[] = [];
  for (const t of tasks) {
    if (t.file.size <= 8 * 1024 * 1024) small.push(t);
    else if (t.file.size <= 256 * 1024 * 1024) medium.push(t);
    else large.push(t);
  }
  // round-robin merge to avoid starvation
  const merged: FileTask[] = [];
  while (small.length || medium.length || large.length) {
    if (small.length) merged.push(small.shift()!);
    if (medium.length) merged.push(medium.shift()!);
    if (large.length) merged.push(large.shift()!);
  }
  return merged;
}

async function uploadOnce(t: FileTask, pick: (n: number) => any, abortSignal?: AbortSignal) {
  const client = new BlockBlobClient(t.sasUrl);
  const { blockSize, concurrency, maxSingleShotSize } = pick(t.file.size);
  await client.uploadBrowserData(t.file, {
    blockSize,
    concurrency,
    maxSingleShotSize,
    blobHTTPHeaders: t.contentType ? { blobContentType: t.contentType } : undefined,
    onProgress: (p) => t.onProgress?.(Math.min(99, Math.round((p.loadedBytes / t.file.size) * 100))),
    abortSignal,
  });
  if (t.tags && Object.keys(t.tags).length) {
    try { await client.setTags(t.tags); } catch {}
  }
  if (t.onProgress) t.onProgress(100);
  return client;
}

async function withRetries<T>(fn: () => Promise<T>, retries = 3) {
  let attempt = 0; let lastErr: any;
  while (attempt <= retries) {
    try { return await fn(); } catch (e) {
      lastErr = e; if (attempt === retries) break;
      const backoff = Math.min(1000 * Math.pow(2, attempt), 8000) + Math.floor(Math.random() * 250);
      await new Promise((r) => setTimeout(r, backoff));
      attempt++;
    }
  }
  throw lastErr;
}

export async function uploadBatch(tasks: FileTask[], opts: SchedulerOptions = {}) {
  const limit = pLimit(opts.maxConcurrentFiles ?? 6);
  const ordered = bucketsBySize(tasks);
  const pick = opts.pickTransferOptions ?? defaultPick;
  const retries = opts.maxRetries ?? 3;
  const verify = opts.verifyHead !== false;

  const settled = await Promise.allSettled(
    ordered.map((t) =>
      limit(async () => {
        const client = await withRetries(() => uploadOnce(t, pick, opts.abortSignal), retries);
        if (verify) {
          const props = await client.getProperties();
          if (typeof t.file.size === 'number' && props.contentLength !== undefined && props.contentLength !== t.file.size) {
            throw new Error(`Size mismatch: local=${t.file.size} remote=${props.contentLength}`);
          }
        }
        if (opts.afterUpload) await opts.afterUpload(t, client);
      })
    )
  );

  return settled; // caller may inspect rejections
}