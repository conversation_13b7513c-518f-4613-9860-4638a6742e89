# Monorepo (vercelweb/ + azurefunctions/) .gitignore

# ─────────────────────────────────────────────────────────────
# Node: dependencies & package managers
**/node_modules/
/.pnp
/.pnp.*
/.yarn/*
!/.yarn/patches
!/.yarn/plugins
!/.yarn/releases
!/.yarn/versions

# ─────────────────────────────────────────────────────────────
# Build outputs
# **/dist/
**/build/

# ─────────────────────────────────────────────────────────────
# Next.js (Vercel app under vercelweb/)
**/.next/
**/out/
next-env.d.ts

# ─────────────────────────────────────────────────────────────
# Coverage / testing
**/coverage/

# ─────────────────────────────────────────────────────────────
# Logs & diagnostics
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# ─────────────────────────────────────────────────────────────
# TypeScript
*.tsbuildinfo

# ─────────────────────────────────────────────────────────────
# Environment files (do not commit secrets)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
**/.env*

# ─────────────────────────────────────────────────────────────
# Vercel
.vercel

# ─────────────────────────────────────────────────────────────
# Azure Functions (Node/TS)
azurefunctions/**/local.settings.json
azurefunctions/**/bin/
azurefunctions/**/obj/

# ─────────────────────────────────────────────────────────────
# OS / Editor
.DS_Store
*.pem

# ─────────────────────────────────────────────────────────────
# Misc project files
.serena/*
.serena
.claude/
repomix-output.xml
CLAUDE.md
TODO
bun.lock
bun.lockb
patch
cmd
remark
patchv2
patchv3
patchv4