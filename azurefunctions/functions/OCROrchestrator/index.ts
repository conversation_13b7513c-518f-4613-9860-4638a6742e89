// Simple orchestration function for manual OCR batch processing
//azurefunctions/functions/OCROrchestrator/index.ts
import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { QueueClient } from '@azure/storage-queue';

const queue = new QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING!, process.env.OCR_QUEUE_NAME || 'ocr-jobs');

// HTTP trigger to start batch OCR processing (for testing/manual trigger)
app.http('StartOCRBatch', {
  methods: ['POST'],
  route: 'orchestration/ocr/batch',
  handler: async (request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> => {
    try {
      const body = await request.json() as { jobs?: { url: string }[] };
      
      if (!body.jobs || !Array.isArray(body.jobs)) {
        return { 
          status: 400, 
          jsonBody: { error: 'Invalid request body. Expected { jobs: [{ url: string }] }' }
        };
      }
      
      context.log(`[INFO] Starting batch processing of ${body.jobs.length} OCR jobs`);
      
      const queuedJobs: { url: string; success: boolean; error?: string }[] = [];
      
      for (const job of body.jobs) {
        try {
          const payload = {
            url: job.url,
            correlationId: `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            time: new Date().toISOString(),
            subject: `manual-batch`,
          };
          
          const body = Buffer.from(JSON.stringify(payload)).toString('base64');
          await queue.sendMessage(body);
          
          queuedJobs.push({ url: job.url, success: true });
          context.log(`[INFO] Queued job for: ${job.url}`);
        } catch (error) {
          const errorMsg = String(error);
          queuedJobs.push({ url: job.url, success: false, error: errorMsg });
          context.log(`[ERROR] Failed to queue job for ${job.url}:`, error);
        }
      }
      
      const successful = queuedJobs.filter(j => j.success).length;
      const failed = queuedJobs.filter(j => !j.success).length;
      
      return { 
        status: 200,
        jsonBody: { 
          message: `Batch processing initiated`,
          total: body.jobs.length,
          successful,
          failed,
          jobs: queuedJobs
        }
      };
    } catch (error) {
      context.log(`[ERROR] Failed to start batch processing:`, error);
      return { 
        status: 500, 
        jsonBody: { error: `Failed to start batch processing: ${error}` }
      };
    }
  }
});

// HTTP trigger to get queue status
app.http('GetOCRStatus', {
  methods: ['GET'],
  route: 'orchestration/ocr/status',
  handler: async (request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> => {
    try {
      const properties = await queue.getProperties();
      
      return { 
        status: 200,
        jsonBody: { 
          queueName: process.env.OCR_QUEUE_NAME || 'ocr-jobs',
          approximateMessageCount: properties.approximateMessagesCount,
          created: properties.metadata?.created || 'unknown',
          status: 'active'
        }
      };
    } catch (error) {
      context.log(`[ERROR] Failed to get queue status:`, error);
      return { 
        status: 500, 
        jsonBody: { error: `Failed to get queue status: ${error}` }
      };
    }
  }
});