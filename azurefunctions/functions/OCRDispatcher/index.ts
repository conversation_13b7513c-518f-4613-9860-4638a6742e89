//azurefunctions/functions/OCRDispatcher/index.ts
import { app, EventGridEvent, InvocationContext } from '@azure/functions';
import { QueueClient } from '@azure/storage-queue';
import * as crypto from 'crypto';

const queue = new QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING!, process.env.OCR_QUEUE_NAME || 'ocr-jobs');
const dlq = new QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING!, (process.env.OCR_QUEUE_NAME || 'ocr-jobs') + '-deadletter');

// simple in-memory dedupe cache (5 min). For production, use Redis/Table.
const seen = new Map<string, number>();
const DEDUPE_TTL_MS = 5 * 60 * 1000;

function keyOf(e: any) {
  const id = e?.id || '';
  const api = e?.data?.api || '';
  const etag = e?.data?.eTag || '';
  const seq = e?.data?.sequencer || '';
  return crypto.createHash('sha1').update(`${id}|${api}|${etag}|${seq}`).digest('hex');
}

function keep(key: string) {
  const now = Date.now();
  for (const [k, ts] of Array.from(seen.entries())) if (now - ts > DEDUPE_TTL_MS) seen.delete(k);
  if (seen.has(key)) return false; seen.set(key, now); return true;
}

// Subscribed to Storage BlobCreated events from the `trial` container (subject filters recommended)
app.eventGrid('OCRDispatcher', {
  handler: async (event: EventGridEvent, context: InvocationContext): Promise<void> => {
    context.log(`[INFO] Processing Event Grid event: ${event.id}`);

    const type = event?.eventType as string;
    if (type !== 'Microsoft.Storage.BlobCreated') {
      context.log(`[DEBUG] Skipping event type: ${type}`);
      return; // ignore delete/soft-delete, etc.
    }

    const url = event?.data?.url as string | undefined;
    if (!url) {
      context.log(`[WARN] No URL in event data`);
      return;
    }
    
    if (!/\.(pdf|pptx|jpg|jpeg|png|tif|tiff)$/i.test(url)) {
      context.log(`[DEBUG] Skipping non-supported file: ${url}`);
      return; // centralized allowlist
    }

    const dedupeKey = keyOf(event);
    if (!keep(dedupeKey)) {
      context.log(`[DEBUG] Duplicate event detected, skipping: ${dedupeKey}`);
      return; // drop duplicates within TTL
    }

    // minimal payload to stay far below 64KB queue limit
    const payload = {
      url,
      eTag: event?.data?.eTag,
      sequencer: event?.data?.sequencer,
      correlationId: event?.id,
      subject: event?.subject,
      time: event?.eventTime,
    };

    try {
      const body = Buffer.from(JSON.stringify(payload)).toString('base64');
      await queue.sendMessage(body);
      context.log(`[INFO] Queued OCR job for: ${url}`);
    } catch (err) {
      context.log(`[WARN] Enqueue failed, send to DLQ:`, err);
      try { 
        await dlq.sendMessage(Buffer.from(JSON.stringify({ error: String(err), payload })).toString('base64')); 
      } catch (dlqErr) {
        context.log(`[ERROR] Failed to send to DLQ:`, dlqErr);
      }
    }
  }
});