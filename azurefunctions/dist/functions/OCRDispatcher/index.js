"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const functions_1 = require("@azure/functions");
const storage_queue_1 = require("@azure/storage-queue");
const crypto = __importStar(require("crypto"));
const queue = new storage_queue_1.QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING, process.env.OCR_QUEUE_NAME || 'ocr-jobs');
const dlq = new storage_queue_1.QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING, (process.env.OCR_QUEUE_NAME || 'ocr-jobs') + '-deadletter');
// simple in-memory dedupe cache (5 min). For production, use Redis/Table.
const seen = new Map();
const DEDUPE_TTL_MS = 5 * 60 * 1000;
function keyOf(e) {
    const id = e?.id || '';
    const api = e?.data?.api || '';
    const etag = e?.data?.eTag || '';
    const seq = e?.data?.sequencer || '';
    return crypto.createHash('sha1').update(`${id}|${api}|${etag}|${seq}`).digest('hex');
}
function keep(key) {
    const now = Date.now();
    for (const [k, ts] of Array.from(seen.entries()))
        if (now - ts > DEDUPE_TTL_MS)
            seen.delete(k);
    if (seen.has(key))
        return false;
    seen.set(key, now);
    return true;
}
// Subscribed to Storage BlobCreated events from the `trial` container (subject filters recommended)
functions_1.app.eventGrid('OCRDispatcher', {
    handler: async (event, context) => {
        context.log(`[INFO] Processing Event Grid event: ${event.id}`);
        const type = event?.eventType;
        if (type !== 'Microsoft.Storage.BlobCreated') {
            context.log(`[DEBUG] Skipping event type: ${type}`);
            return; // ignore delete/soft-delete, etc.
        }
        const url = event?.data?.url;
        if (!url) {
            context.log(`[WARN] No URL in event data`);
            return;
        }
        if (!/\.(pdf|pptx|jpg|jpeg|png|tif|tiff)$/i.test(url)) {
            context.log(`[DEBUG] Skipping non-supported file: ${url}`);
            return; // centralized allowlist
        }
        const dedupeKey = keyOf(event);
        if (!keep(dedupeKey)) {
            context.log(`[DEBUG] Duplicate event detected, skipping: ${dedupeKey}`);
            return; // drop duplicates within TTL
        }
        // minimal payload to stay far below 64KB queue limit
        const payload = {
            url,
            eTag: event?.data?.eTag,
            sequencer: event?.data?.sequencer,
            correlationId: event?.id,
            subject: event?.subject,
            time: event?.eventTime,
        };
        try {
            const body = Buffer.from(JSON.stringify(payload)).toString('base64');
            await queue.sendMessage(body);
            context.log(`[INFO] Queued OCR job for: ${url}`);
        }
        catch (err) {
            context.log(`[WARN] Enqueue failed, send to DLQ:`, err);
            try {
                await dlq.sendMessage(Buffer.from(JSON.stringify({ error: String(err), payload })).toString('base64'));
            }
            catch (dlqErr) {
                context.log(`[ERROR] Failed to send to DLQ:`, dlqErr);
            }
        }
    }
});
