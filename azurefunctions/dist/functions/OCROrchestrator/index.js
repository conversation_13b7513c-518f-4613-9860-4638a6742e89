"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Simple orchestration function for manual OCR batch processing
const functions_1 = require("@azure/functions");
const storage_queue_1 = require("@azure/storage-queue");
const queue = new storage_queue_1.QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING, process.env.OCR_QUEUE_NAME || 'ocr-jobs');
// HTTP trigger to start batch OCR processing (for testing/manual trigger)
functions_1.app.http('StartOCRBatch', {
    methods: ['POST'],
    route: 'orchestration/ocr/batch',
    handler: async (request, context) => {
        try {
            const body = await request.json();
            if (!body.jobs || !Array.isArray(body.jobs)) {
                return {
                    status: 400,
                    jsonBody: { error: 'Invalid request body. Expected { jobs: [{ url: string }] }' }
                };
            }
            context.log(`[INFO] Starting batch processing of ${body.jobs.length} OCR jobs`);
            const queuedJobs = [];
            for (const job of body.jobs) {
                try {
                    const payload = {
                        url: job.url,
                        correlationId: `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                        time: new Date().toISOString(),
                        subject: `manual-batch`,
                    };
                    const body = Buffer.from(JSON.stringify(payload)).toString('base64');
                    await queue.sendMessage(body);
                    queuedJobs.push({ url: job.url, success: true });
                    context.log(`[INFO] Queued job for: ${job.url}`);
                }
                catch (error) {
                    const errorMsg = String(error);
                    queuedJobs.push({ url: job.url, success: false, error: errorMsg });
                    context.log(`[ERROR] Failed to queue job for ${job.url}:`, error);
                }
            }
            const successful = queuedJobs.filter(j => j.success).length;
            const failed = queuedJobs.filter(j => !j.success).length;
            return {
                status: 200,
                jsonBody: {
                    message: `Batch processing initiated`,
                    total: body.jobs.length,
                    successful,
                    failed,
                    jobs: queuedJobs
                }
            };
        }
        catch (error) {
            context.log(`[ERROR] Failed to start batch processing:`, error);
            return {
                status: 500,
                jsonBody: { error: `Failed to start batch processing: ${error}` }
            };
        }
    }
});
// HTTP trigger to get queue status
functions_1.app.http('GetOCRStatus', {
    methods: ['GET'],
    route: 'orchestration/ocr/status',
    handler: async (request, context) => {
        try {
            const properties = await queue.getProperties();
            return {
                status: 200,
                jsonBody: {
                    queueName: process.env.OCR_QUEUE_NAME || 'ocr-jobs',
                    approximateMessageCount: properties.approximateMessagesCount,
                    created: properties.metadata?.created || 'unknown',
                    status: 'active'
                }
            };
        }
        catch (error) {
            context.log(`[ERROR] Failed to get queue status:`, error);
            return {
                status: 500,
                jsonBody: { error: `Failed to get queue status: ${error}` }
            };
        }
    }
});
