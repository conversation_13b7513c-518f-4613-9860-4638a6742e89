"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const functions_1 = require("@azure/functions");
const node_fetch_1 = __importDefault(require("node-fetch"));
const storage_blob_1 = require("@azure/storage-blob");
// Enhanced retry patterns from patch with exponential backoff
const BACKOFF_SEQ = [2000, 5000, 13000, 34000]; // 2→5→13→34s (patch recommendation)
const POLL_BASE_DELAY_MS = 2000;
async function withRetry(fn) {
    let attempt = 0;
    for (;;) {
        try {
            return await fn();
        }
        catch (err) {
            const status = err?.status || err?.response?.status;
            if (status === 429 || (status >= 500 && status < 600)) {
                const delay = BACKOFF_SEQ[Math.min(attempt, BACKOFF_SEQ.length - 1)];
                await new Promise(r => setTimeout(r, delay));
                attempt++;
                continue;
            }
            throw err;
        }
    }
}
const blobService = storage_blob_1.BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);
functions_1.app.storageQueue('OCRWorker', {
    queueName: process.env.OCR_QUEUE_NAME || 'ocr-jobs',
    connection: 'AZURE_STORAGE_CONNECTION_STRING',
    handler: async (queueEntry, context) => {
        const queueItem = queueEntry;
        const startTime = Date.now();
        try {
            const { url, correlationId, eTag, sequencer } = JSON.parse(Buffer.from(queueItem, 'base64').toString('utf8'));
            context.log(`[INFO] Processing OCR job for URL: ${url} (correlation: ${correlationId})`);
            // Build a read-only SAS (short TTL) for DI to fetch the source document if needed
            const src = new URL(url);
            const accountUrl = `${src.protocol}//${src.host}`;
            // Analyze via Document Intelligence v4 - prebuilt-layout with markdown output
            const analyzeUrl = `${process.env.DI_ENDPOINT}/documentintelligence/documentModels/prebuilt-layout:analyze?api-version=2024-11-30&outputContentFormat=markdown`;
            context.log(`[DEBUG] Calling Document Intelligence: ${analyzeUrl}`);
            const analyzeReq = await withRetry(async () => (0, node_fetch_1.default)(analyzeUrl, {
                method: 'POST',
                headers: {
                    'Ocp-Apim-Subscription-Key': process.env.DI_KEY,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ urlSource: url }),
            }));
            if (!analyzeReq.ok) {
                const errorText = await analyzeReq.text();
                throw Object.assign(new Error(`Analyze submit failed: ${analyzeReq.status} ${errorText}`), { status: analyzeReq.status });
            }
            const operationLocation = analyzeReq.headers.get('operation-location');
            context.log(`[DEBUG] Operation location: ${operationLocation}`);
            // Poll with backoff; respect retry-after when present
            const start = Date.now();
            let content = '';
            let pollCount = 0;
            while (true) {
                pollCount++;
                // Initial delay before first poll (recommended by DI best practices)
                await new Promise((r) => setTimeout(r, POLL_BASE_DELAY_MS));
                const r = await withRetry(async () => (0, node_fetch_1.default)(operationLocation, {
                    headers: { 'Ocp-Apim-Subscription-Key': process.env.DI_KEY }
                }));
                // Handle 429 specifically for polling (patch enhancement)
                if (r.status === 429) {
                    await new Promise(r => setTimeout(r, POLL_BASE_DELAY_MS));
                    continue;
                }
                if (!r.ok) {
                    throw Object.assign(new Error(`Poll request failed: ${r.status} ${await r.text()}`), { status: r.status });
                }
                // Respect retry-after header (patch best practice)
                const ra = parseInt(r.headers.get('retry-after') || '0', 10);
                const j = await r.json();
                context.log(`[DEBUG] Poll ${pollCount}, status: ${j.status}`);
                if (j.status === 'succeeded') {
                    // Enhanced content extraction (patch pattern)
                    content = j?.analyzeResult?.content ?? j?.content ?? '';
                    context.log(`[INFO] Document Intelligence completed after ${pollCount} polls, content length: ${content.length}`);
                    break;
                }
                if (j.status === 'failed') {
                    throw new Error(`Analyze failed: ${JSON.stringify(j?.error ?? j)}`);
                }
                // Enhanced timeout logic with retry-after consideration
                const delay = Math.max(POLL_BASE_DELAY_MS, ra * 1000);
                await new Promise(res => setTimeout(res, delay));
                // Timeout after 9 minutes (function timeout is 10 minutes)
                if (Date.now() - start > 9 * 60 * 1000) {
                    throw new Error(`Analyze timeout after ${pollCount} polls`);
                }
            }
            if (!content.trim()) {
                context.log(`[WARN] Empty content returned for ${url}`);
                return;
            }
            // Write markdown to target container with .md extension
            const path = src.pathname.replace(/^\//, '');
            const filename = path.split('/').pop();
            const mdName = path.replace(/\.[^./]+$/, '') + '.md';
            const container = blobService.getContainerClient(process.env.DEST_CONTAINER || 'markdown');
            // Ensure container exists
            await container.createIfNotExists();
            const mdClient = container.getBlockBlobClient(mdName);
            // Check if already processed (patch deduplication pattern)
            if (await mdClient.exists()) {
                const props = await mdClient.getProperties();
                const tagRes = await mdClient.getTags().catch(() => undefined);
                const processedETag = tagRes?.tags?.sourceEtag || props.metadata?.sourceEtag;
                if (processedETag && processedETag === eTag) {
                    context.log(`[INFO] Skip already processed: ${mdName} (sourceETag: ${eTag})`);
                    return;
                }
            }
            await mdClient.upload(content, Buffer.byteLength(content), {
                blobHTTPHeaders: {
                    blobContentType: 'text/markdown; charset=utf-8',
                    blobCacheControl: 'no-cache'
                },
                metadata: {
                    sourceName: filename,
                    sourceUrl: url,
                    sourceEtag: eTag || '', // Track source ETag for deduplication (patch requirement)
                    model: 'prebuilt-layout',
                    contentFormat: 'markdown',
                    processedAt: new Date().toISOString(),
                    correlationId: correlationId || '',
                    sequencer: sequencer || '',
                    processingTimeMs: String(Date.now() - startTime),
                    pollCount: String(pollCount)
                },
            });
            // Write Blob Index Tags for better deduplication (patch enhancement)
            if (eTag) {
                await mdClient.setTags({ sourceEtag: eTag });
            }
            const processingTime = Date.now() - startTime;
            context.log(`[SUCCESS] OCR completed: ${filename} -> ${mdName} (${content.length} chars, ${processingTime}ms, ${pollCount} polls)`);
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            context.log(`[ERROR] OCR processing failed after ${processingTime}ms:`, error);
            throw error; // Will cause message to be moved to poison queue after retries
        }
    }
});
