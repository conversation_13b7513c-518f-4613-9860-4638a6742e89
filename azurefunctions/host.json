{"version": "2.0", "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "concurrency": {"dynamicConcurrencyEnabled": true}, "extensions": {"queues": {"batchSize": 16, "newBatchThreshold": 8, "maxPollingInterval": "00:00:02", "visibilityTimeout": "00:02:00"}, "durableTask": {"hubName": "ocrHub", "maxConcurrentActivityFunctions": 8, "maxConcurrentOrchestratorFunctions": 2}}, "functionTimeout": "00:10:00"}