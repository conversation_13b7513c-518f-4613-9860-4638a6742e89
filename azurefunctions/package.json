{"name": "ocr-on-upload", "private": true, "type": "commonjs", "main": "dist/functions/**/*.js", "sideEffects": true, "engines": {"node": ">=20 <23"}, "scripts": {"build": "tsc -p .", "start": "func start", "test": "echo no-tests && exit 0"}, "dependencies": {"@azure-rest/ai-document-intelligence": "^1.1.0", "@azure/core-auth": "^1.10.0", "@azure/functions": "^4.7.3", "@azure/storage-blob": "^12.28.0", "@azure/storage-queue": "^12.27.0", "crypto": "^1.0.1", "durable-functions": "^3.2.0", "node-fetch": "^3.3.2", "unzipper": "^0.10.14"}, "devDependencies": {"typescript": "^5.9.2", "@types/node": "^20.19.11"}}